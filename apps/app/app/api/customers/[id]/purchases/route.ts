import { NextRequest } from 'next/server';
import { db, customers, customerPackages, packages } from '@workspace/auth/server';
import { eq, and, desc, asc, count } from 'drizzle-orm';
import {
  createPurchaseSchema,
  purchaseQuerySchema,
  type PaginatedResponse,
  type PurchaseResponse,
} from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  validateQueryParams,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { addCreditsFromPurchase } from '@/lib/credit-service';

// GET /api/customers/[id]/purchases - Get customer purchase history
export const GET = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id: customerId } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Verify customer belongs to trainer
    const [customer] = await db
      .select()
      .from(customers)
      .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

    if (!customer) {
      return createErrorResponse('Not Found', 'Customer not found', 404);
    }

    const queryValidation = validateQueryParams(request, purchaseQuerySchema);
    if (!queryValidation.success) {
      return createErrorResponse(
        queryValidation.error.error,
        queryValidation.error.message,
        400,
        queryValidation.error.details
      );
    }

    const { limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build order by - ensure we have a valid column
    const validSortBy = sortBy || 'purchaseDate';
    const orderDirection = sortOrder === 'desc' ? desc : asc;

    // Map sortBy to actual columns
    const sortColumns = {
      purchaseDate: customerPackages.purchaseDate,
    };

    const orderByColumn = sortColumns[validSortBy as keyof typeof sortColumns] || customerPackages.purchaseDate;

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(customerPackages)
      .innerJoin(packages, eq(customerPackages.packageId, packages.id))
      .where(eq(customerPackages.customerId, customerId));

    if (!totalResult) {
      throw new Error('Failed to fetch total count');
    }

    const total = totalResult.count;

    // Get package assignments with package details
    const packageAssignments = await db
      .select({
        id: customerPackages.id,
        customerId: customerPackages.customerId,
        packageId: customerPackages.packageId,
        sessionsPurchased: packages.sessionCount,
        purchaseDate: customerPackages.purchaseDate,
        createdAt: customerPackages.createdAt,
        updatedAt: customerPackages.updatedAt,
        packageName: packages.name,
        packageDescription: packages.description,
        packagePrice: packages.price,
      })
      .from(customerPackages)
      .innerJoin(packages, eq(customerPackages.packageId, packages.id))
      .where(eq(customerPackages.customerId, customerId))
      .orderBy(orderDirection(orderByColumn))
      .limit(limit || 20)
      .offset(offset || 0);

    const validLimit = limit || 20;
    const validOffset = offset || 0;

    const response: PaginatedResponse<PurchaseResponse> = {
      data: packageAssignments.map((assignment) => ({
        id: assignment.id,
        customerId: assignment.customerId,
        packageId: assignment.packageId,
        sessionsPurchased: assignment.sessionsPurchased,
        purchaseDate: assignment.purchaseDate,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt,
        packageName: assignment.packageName,
        packageDescription: assignment.packageDescription,
        packagePrice: assignment.packagePrice,
      })),
      pagination: {
        total,
        limit: validLimit,
        offset: validOffset,
        hasMore: validOffset + validLimit < total,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// POST /api/customers/[id]/purchases - Record new purchase
// export const POST = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
//   try {
//     const { id: customerId } = await params;
//     const trainerId = await getTrainerIdFromUser(user);

//     if (!trainerId) {
//       return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
//     }

//     // Verify customer belongs to trainer
//     const [customer] = await db
//       .select()
//       .from(customers)
//       .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

//     if (!customer) {
//       return createErrorResponse('Not Found', 'Customer not found', 404);
//     }

//     const bodyValidation = await validateRequestBody(request, createPurchaseSchema);
//     if (!bodyValidation.success) {
//       return createErrorResponse(
//         bodyValidation.error.error,
//         bodyValidation.error.message,
//         400,
//         bodyValidation.error.details
//       );
//     }

//     const purchaseData = bodyValidation.data;

//     // Create purchase record
//     const [newPurchase] = await db
//       .insert(customerPurchases)
//       .values({
//         customerId,
//         sessionsPurchased: purchaseData.sessionsPurchased,
//         amountPaid: purchaseData.amountPaid.toString(),
//         paymentStatus: purchaseData.paymentStatus,
//       })
//       .returning();

//     if (!newPurchase) {
//       throw new Error('Failed to create purchase');
//     }

//     // Add credits using credit transaction service (only if payment is completed)
//     if (purchaseData.paymentStatus === 'completed') {
//       await addCreditsFromPurchase(customerId, newPurchase.id, purchaseData.sessionsPurchased);
//     }

//     const result = newPurchase;

//     if (!result) {
//       throw new Error('Failed to create purchase');
//     }

//     const response: PurchaseResponse = {
//       id: result.id,
//       customerId: result.customerId,
//       packageId: null, // Manually set to null as this is a direct purchase, not package assignment
//       sessionsPurchased: result.sessionsPurchased,
//       amountPaid: result.amountPaid,
//       purchaseDate: result.purchaseDate,
//       paymentStatus: result.paymentStatus,
//       createdAt: result.createdAt,
//       updatedAt: result.updatedAt,
//     };

//     return createSuccessResponse(response, 201);
//   } catch (error) {
//     return handleApiError(error);
//   }
// });
