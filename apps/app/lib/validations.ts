import { z } from 'zod';

// Customer validation schemas
export const createCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
  parentName: z.string().optional().or(z.literal('')),
});

export const updateCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters').optional(),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().optional().or(z.literal('')),
  parentName: z.string().optional().or(z.literal('')),
});

export const customerQuerySchema = z.object({
  search: z.string().optional(),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  sortBy: z.enum(['name', 'email', 'createdAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Purchase validation schemas
export const createPurchaseSchema = z.object({
  sessionsPurchased: z.number().int().min(1, 'Must purchase at least 1 session'),
  amountPaid: z.number().min(0, 'Amount paid must be non-negative'),
  paymentStatus: z.enum(['completed', 'pending', 'failed']),
});

export const purchaseQuerySchema = z.object({
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  sortBy: z.enum(['purchaseDate']).default('purchaseDate'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Package validation schemas
export const createPackageSchema = z.object({
  name: z.string().min(1, 'Package name is required').max(100, 'Package name must be less than 100 characters'),
  description: z.string().optional().or(z.literal('')),
  sessionCount: z.number().int().min(1, 'Session count must be at least 1').max(100, 'Session count cannot exceed 100'),
  price: z.number().min(0, 'Price must be non-negative'),
  isActive: z.boolean().default(true).optional(),
});

export const updatePackageSchema = z.object({
  name: z
    .string()
    .min(1, 'Package name is required')
    .max(100, 'Package name must be less than 100 characters')
    .optional(),
  description: z.string().optional().or(z.literal('')),
  sessionCount: z
    .number()
    .int()
    .min(1, 'Session count must be at least 1')
    .max(100, 'Session count cannot exceed 100')
    .optional(),
  price: z.number().min(0, 'Price must be non-negative').optional(),
  isActive: z.boolean().optional(),
});

export const packageQuerySchema = z.object({
  search: z.string().optional(),
  isActive: z.preprocess((val) => {
    if (val === 'true') return true;
    if (val === 'false') return false;
    return val;
  }, z.boolean().optional()),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  sortBy: z.enum(['name', 'sessionCount', 'price', 'createdAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Customer package validation schemas
export const assignPackageSchema = z.object({
  packageId: z.string().uuid('Invalid package ID'),
  purchaseId: z.string().uuid().optional(), // Optional link to purchase record
});

export const customerPackageQuerySchema = z.object({
  // customerId: z.string().uuid('Invalid customer ID'),
  includeExpired: z.boolean().default(false), // Whether to include packages with 0 sessions
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  sortBy: z.enum(['purchaseDate', 'sessionsRemaining', 'packageName']).default('purchaseDate'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Workout validation schemas
export const createWorkoutSchema = z
  .object({
    title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
    description: z.string().optional().or(z.literal('')),
    startTime: z.string().datetime('Invalid start time format'),
    endTime: z.string().datetime('Invalid end time format'),
    minParticipants: z
      .number()
      .int()
      .min(1, 'Minimum participants must be at least 1')
      .max(10, 'Minimum participants cannot exceed 10')
      .default(3),
    maxParticipants: z
      .number()
      .int()
      .min(1, 'Maximum participants must be at least 1')
      .max(10, 'Maximum participants cannot exceed 10')
      .default(5),
    location: z.string().optional().or(z.literal('')),
  })
  .refine(
    (data) => {
      const start = new Date(data.startTime);
      const end = new Date(data.endTime);
      return end > start;
    },
    {
      message: 'End time must be after start time',
      path: ['endTime'],
    }
  )
  .refine(
    (data) => {
      const start = new Date(data.startTime);
      const end = new Date(data.endTime);
      const durationMs = end.getTime() - start.getTime();
      const durationHours = durationMs / (1000 * 60 * 60);
      return durationHours === 1;
    },
    {
      message: 'Workout sessions must be exactly 1 hour long',
      path: ['endTime'],
    }
  )
  .refine((data) => data.maxParticipants >= data.minParticipants, {
    message: 'Maximum participants must be greater than or equal to minimum participants',
    path: ['maxParticipants'],
  });

export const updateWorkoutSchema = z
  .object({
    title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters').optional(),
    description: z.string().optional().or(z.literal('')),
    startTime: z.string().datetime('Invalid start time format').optional(),
    endTime: z.string().datetime('Invalid end time format').optional(),
    minParticipants: z
      .number()
      .int()
      .min(1, 'Minimum participants must be at least 1')
      .max(10, 'Minimum participants cannot exceed 10')
      .optional(),
    maxParticipants: z
      .number()
      .int()
      .min(1, 'Maximum participants must be at least 1')
      .max(10, 'Maximum participants cannot exceed 10')
      .optional(),
    status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled']).optional(),
    location: z.string().optional().or(z.literal('')),
  })
  .refine(
    (data) => {
      if (data.startTime && data.endTime) {
        const start = new Date(data.startTime);
        const end = new Date(data.endTime);
        return end > start;
      }
      return true;
    },
    {
      message: 'End time must be after start time',
      path: ['endTime'],
    }
  )
  .refine(
    (data) => {
      if (data.startTime && data.endTime) {
        const start = new Date(data.startTime);
        const end = new Date(data.endTime);
        const durationMs = end.getTime() - start.getTime();
        const durationHours = durationMs / (1000 * 60 * 60);
        return durationHours === 1;
      }
      return true;
    },
    {
      message: 'Workout sessions must be exactly 1 hour long',
      path: ['endTime'],
    }
  )
  .refine(
    (data) => {
      if (data.minParticipants && data.maxParticipants) {
        return data.maxParticipants >= data.minParticipants;
      }
      return true;
    },
    {
      message: 'Maximum participants must be greater than or equal to minimum participants',
      path: ['maxParticipants'],
    }
  );

export const workoutQuerySchema = z.object({
  search: z.string().optional(),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled']).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  limit: z.coerce.number().int().min(1).max(100).default(50),
  offset: z.coerce.number().int().min(0).default(0),
  sortBy: z.enum(['startTime', 'title', 'status', 'createdAt']).default('startTime'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Workout participant validation schemas
export const addParticipantSchema = z.object({
  customerId: z.string().uuid('Invalid customer ID'),
});

export const updateParticipantSchema = z.object({
  status: z.enum(['enrolled', 'confirmed', 'cancelled']),
});

// Credit transaction validation schemas
export const createCreditTransactionSchema = z.object({
  customerId: z.string().uuid('Invalid customer ID'),
  type: z.enum(['purchase', 'deduction', 'refund', 'adjustment']),
  amount: z.number().int(),
  description: z.string().min(1, 'Description is required'),
  relatedPurchaseId: z.string().uuid().optional(),
  relatedWorkoutId: z.string().uuid().optional(),
  relatedParticipantId: z.string().uuid().optional(),
});

export const creditTransactionQuerySchema = z.object({
  customerId: z.string().uuid().optional(),
  type: z.enum(['purchase', 'deduction', 'refund', 'adjustment']).optional(),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  offset: z.coerce.number().int().min(0).default(0),
  sortBy: z.enum(['createdAt', 'amount', 'type']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// API response schemas
export const customerResponseSchema = z.object({
  id: z.string().uuid(),
  trainerId: z.string().uuid(),
  name: z.string(),
  email: z.string().nullable(),
  phone: z.string().nullable(),
  parentName: z.string().nullable(),
  totalSessions: z.number().int().optional(), // Calculated from packages
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const purchaseResponseSchema = z.object({
  id: z.string().uuid(),
  customerId: z.string().uuid(),
  packageId: z.string().uuid().nullable(),
  sessionsPurchased: z.number().int(),
  purchaseDate: z.date(),
  createdAt: z.date(),
  updatedAt: z.date(),
  packageName: z.string().nullable().optional(), // Package name for package assignments
  packageDescription: z.string().nullable().optional(), // Package description for package assignments
  packagePrice: z.string().nullable().optional(), // Package price for package assignments
});

export const packageResponseSchema = z.object({
  id: z.string().uuid(),
  trainerId: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable(),
  sessionCount: z.number().int(),
  price: z.string(), // Decimal comes as string from DB
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const customerPackageResponseSchema = z.object({
  id: z.string().uuid(),
  customerId: z.string().uuid(),
  packageId: z.string().uuid(),
  packageName: z.string(),
  packageDescription: z.string().nullable(),
  originalSessionCount: z.number().int(),
  sessionsRemaining: z.number().int(),
  purchaseDate: z.date(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const creditTransactionResponseSchema = z.object({
  id: z.string().uuid(),
  customerId: z.string().uuid(),
  type: z.string(),
  amount: z.number().int(),
  balanceBefore: z.number().int(),
  balanceAfter: z.number().int(),
  description: z.string(),
  relatedPurchaseId: z.string().uuid().nullable(),
  relatedWorkoutId: z.string().uuid().nullable(),
  relatedParticipantId: z.string().uuid().nullable(),
  createdAt: z.date(),
});

export const workoutResponseSchema = z.object({
  id: z.string().uuid(),
  trainerId: z.string().uuid(),
  title: z.string(),
  description: z.string().nullable(),
  startTime: z.date(),
  endTime: z.date(),
  minParticipants: z.number().int(),
  maxParticipants: z.number().int(),
  status: z.string(),
  location: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
  participants: z
    .array(
      z.object({
        id: z.string().uuid(),
        customerId: z.string().uuid(),
        customerName: z.string(),
        status: z.string(),
        enrolledAt: z.date(),
        confirmedAt: z.date().nullable(),
        creditDeducted: z.boolean(),
      })
    )
    .optional(),
  participantCount: z.number().int().optional(),
});

export const workoutParticipantResponseSchema = z.object({
  id: z.string().uuid(),
  workoutId: z.string().uuid(),
  customerId: z.string().uuid(),
  customerName: z.string(),
  status: z.string(),
  enrolledAt: z.date(),
  confirmedAt: z.date().nullable(),
  creditDeducted: z.boolean(),
});

export const paginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema),
    pagination: z.object({
      total: z.number().int(),
      limit: z.number().int(),
      offset: z.number().int(),
      hasMore: z.boolean(),
    }),
  });

// Error response schema
export const errorResponseSchema = z.object({
  error: z.string(),
  message: z.string(),
  details: z.record(z.any()).optional(),
});

// Type exports
export type CreateCustomerInput = z.infer<typeof createCustomerSchema>;
export type UpdateCustomerInput = z.infer<typeof updateCustomerSchema>;
export type CustomerQueryInput = z.infer<typeof customerQuerySchema>;
export type CreatePurchaseInput = z.infer<typeof createPurchaseSchema>;
export type PurchaseQueryInput = z.infer<typeof purchaseQuerySchema>;
export type CreatePackageInput = z.infer<typeof createPackageSchema>;
export type UpdatePackageInput = z.infer<typeof updatePackageSchema>;
export type PackageQueryInput = z.infer<typeof packageQuerySchema>;
export type AssignPackageInput = z.infer<typeof assignPackageSchema>;
export type CustomerPackageQueryInput = z.infer<typeof customerPackageQuerySchema>;
export type CreateCreditTransactionInput = z.infer<typeof createCreditTransactionSchema>;
export type CreditTransactionQueryInput = z.infer<typeof creditTransactionQuerySchema>;
export type CreateWorkoutInput = z.infer<typeof createWorkoutSchema>;
export type UpdateWorkoutInput = z.infer<typeof updateWorkoutSchema>;
export type WorkoutQueryInput = z.infer<typeof workoutQuerySchema>;
export type AddParticipantInput = z.infer<typeof addParticipantSchema>;
export type UpdateParticipantInput = z.infer<typeof updateParticipantSchema>;
export type CustomerResponse = z.infer<typeof customerResponseSchema>;
export type PurchaseResponse = z.infer<typeof purchaseResponseSchema>;
export type PackageResponse = z.infer<typeof packageResponseSchema>;
export type CustomerPackageResponse = z.infer<typeof customerPackageResponseSchema>;
export type CreditTransactionResponse = z.infer<typeof creditTransactionResponseSchema>;
export type WorkoutResponse = z.infer<typeof workoutResponseSchema>;
export type WorkoutParticipantResponse = z.infer<typeof workoutParticipantResponseSchema>;
export type PaginatedResponse<T> = {
  data: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
};
export type ErrorResponse = z.infer<typeof errorResponseSchema>;
