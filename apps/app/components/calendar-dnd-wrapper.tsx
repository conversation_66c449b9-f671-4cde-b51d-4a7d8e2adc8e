'use client';

import { useState, useMemo } from 'react';
import { startOfWeek, endOfWeek } from 'date-fns';
import { useCalendarContext } from '@/components/event-calendar/calendar-context';
import { useWorkoutsList, createWorkout, updateWorkout, deleteWorkout, addParticipant } from '@/hooks/use-workouts';

import {
  type CalendarEvent,
  CalendarDndProvider,
} from '@/components/event-calendar';
import type { CustomerResponse } from '@/lib/validations';
import { hasCustomerConflict, hasWorkoutTimeConflict, formatConflictMessage } from '@/lib/workout-utils';
import { toast } from 'sonner';

interface CalendarDndWrapperProps {
  children: React.ReactNode;
}

export function CalendarDndWrapper({ children }: CalendarDndWrapperProps) {
  const { currentDate } = useCalendarContext();

  // Calculate date range for fetching workouts (current week)
  const startDate = startOfWeek(currentDate, { weekStartsOn: 0 });
  const endDate = endOfWeek(currentDate, { weekStartsOn: 0 });

  // Fetch workouts for the current date range
  const { workouts, mutate } = useWorkoutsList({
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    limit: 100, // Maximum allowed by API
  });

  const handleWorkoutUpdate = async (event: CalendarEvent) => {
    try {
      if (!event.workoutId) return;

      const updatedWorkout = await updateWorkout(event.workoutId, {
        startTime: event.start.toISOString(),
        endTime: event.end.toISOString(),
      });

      if (updatedWorkout) {
        toast.success('Workout updated successfully');
        mutate(); // Refresh the workout list
      }
    } catch (error) {
      console.error('Error updating workout:', error);
      toast.error('Failed to update workout');
    }
  };

  const handleCustomerDrop = async (customer: CustomerResponse, targetDate: Date, targetTime?: Date) => {
    try {
      // Check if customer has sessions
      if ((customer.totalSessions || 0) <= 0) {
        toast.error('Customer has no sessions available');
        return;
      }

      // Calculate target workout time
      const targetDateTime = targetTime || targetDate;
      const workoutStart = new Date(targetDateTime);
      const workoutEnd = new Date(workoutStart);
      workoutEnd.setHours(workoutStart.getHours() + 1); // 1-hour duration

      // Check for customer conflicts
      const { hasConflict, conflictingWorkout } = hasCustomerConflict(customer.id, workoutStart, workoutEnd, workouts);

      if (hasConflict && conflictingWorkout) {
        toast.error(formatConflictMessage(customer.name, conflictingWorkout));
        return;
      }

      // Check if there's an existing workout at this time
      const existingWorkout = workouts.find((workout) => {
        const workoutStart = new Date(workout.startTime);
        const workoutEnd = new Date(workout.endTime);
        const targetStart = new Date(targetDateTime);
        const targetEnd = new Date(targetStart);
        targetEnd.setHours(targetStart.getHours() + 1);

        return (
          (targetStart >= workoutStart && targetStart < workoutEnd) ||
          (targetEnd > workoutStart && targetEnd <= workoutEnd) ||
          (targetStart <= workoutStart && targetEnd >= workoutEnd)
        );
      });

      if (existingWorkout) {
        // Check if workout is at capacity
        const currentParticipants = existingWorkout.participantCount || 0;
        const maxParticipants = existingWorkout.maxParticipants || 5;

        if (currentParticipants >= maxParticipants) {
          toast.error('Workout is at maximum capacity');
          return;
        }

        // Add customer to existing workout
        const newParticipant = await addParticipant(existingWorkout.id, { customerId: customer.id });
        if (newParticipant) {
          toast.success(`${customer.name} added to ${existingWorkout.title}`);
        }
      } else {
        // Check for workout time conflicts before creating new workout
        const { hasConflict: hasTimeConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
          workoutStart,
          workoutEnd,
          workouts
        );

        if (hasTimeConflict && conflictingWorkouts.length > 0) {
          toast.error('Cannot create workout: time slot conflicts with existing workout');
          return;
        }

        // Create new workout and add customer
        const startTime = new Date(targetDateTime);
        const endTime = new Date(startTime);
        endTime.setHours(startTime.getHours() + 1); // 1-hour duration

        const newWorkout = await createWorkout({
          title: `Training Session`,
          description: '',
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          location: '',
          minParticipants: 3,
          maxParticipants: 5,
        });

        // Add customer to the new workout
        if (newWorkout) {
          const newParticipant = await addParticipant(newWorkout.id, { customerId: customer.id });
          if (newParticipant) {
            toast.success(`${customer.name} added to new workout`);
          }
        }
      }

      // Refresh the workout list to show updated data
      mutate();
    } catch (error) {
      console.error('Error handling customer drop:', error);
      toast.error('Failed to add customer to workout');
    }
  };

  return (
    <CalendarDndProvider onEventUpdate={handleWorkoutUpdate} onCustomerDrop={handleCustomerDrop}>
      {children}
    </CalendarDndProvider>
  );
}
